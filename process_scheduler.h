#ifndef PROCESS_SCHEDULER_H
#define PROCESS_SCHEDULER_H

#include <iostream>
#include <fstream>
#include <vector>
#include <queue>
#include <algorithm>

const int N = 5;  // 进程数量

// 进程状态枚举
enum ProcessState {
    READY,
    RUNNING,
    BLOCKED
};

// PCB结构体
struct PCB {
    int ID;
    int PRIORITY;
    int CPUTIME;
    int ALLTIME;
    int STARTBLOCK;
    int BLOCKTIME;
    ProcessState STATE;
    PCB* NEXT;

    PCB() : NEXT(nullptr) {}
};

#endif // PROCESS_SCHEDULER_H
