import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置科研风格的matplotlib参数
rcParams['font.family'] = 'serif'
rcParams['font.serif'] = ['Times New Roman']
rcParams['font.size'] = 20
rcParams['axes.linewidth'] = 1.2
rcParams['axes.spines.top'] = False
rcParams['axes.spines.right'] = False
rcParams['xtick.direction'] = 'in'
rcParams['ytick.direction'] = 'in'
rcParams['figure.dpi'] = 300

# 数据定义
baseline = {'Random': 19.7}

proprietary_models = {
    'GPT-4o-mini[32f]': 36.5,
    'Gemini-2.0-Flash[1 fps]': 38.3,
    'Gemini-1.5-Flash[1 fps]': 40.5,
    'Gemini-1.5-Pro[1 fps]': 42.5,
    'GPT-4o[32f]': 43.6,
    'Qwen-VL-Max-latest[32f]': 45.5
}

opensource_models = {
    'InternVL2-2B[32f]': 27.6,
    'InternVL2-4B[32f]': 28.1,
    'InternVL2-8B[32f]': 28.1,
    'InternVL2-26B[32f]': 28.3,
    'InternVL2-40B[32f]': 28.4,
    'InternVL2-Llama3-76B[32f]': 28.9,
    'Qwen2-VL-2B-Instruct[0.5 fps]': 31.9,
    'Qwen2-VL-7B-Instruct[0.25 fps]': 36.2,
    'LLaVA-NeXT-Video-7B-hf[32f]': 38.6,
    'Phi-3.5-vision-instruct[32f]': 38.7,
    'Kangaroo[64f]': 39.2
}

# 按性能排序开源模型
opensource_sorted = dict(sorted(opensource_models.items(), key=lambda x: x[1]))
proprietary_sorted = dict(sorted(proprietary_models.items(), key=lambda x: x[1]))

# 合并所有数据用于绘图
all_models = {}
all_models.update(baseline)
all_models.update(opensource_sorted)
all_models.update(proprietary_sorted)

# 准备绘图数据
model_names = list(all_models.keys())
scores = list(all_models.values())

# 创建颜色映射
colors = []
for name in model_names:
    if name in baseline:
        colors.append('#808080')  # 灰色 - 基线
    elif name in opensource_models:
        colors.append('#2E86AB')  # 蓝色 - 开源模型
    else:
        colors.append('#A23B72')  # 紫红色 - 商用模型

# 创建图表
fig, ax = plt.subplots(figsize=(14, 8))

# 绘制直方图
bars = ax.bar(range(len(model_names)), scores, color=colors, alpha=0.8, 
              edgecolor='black', linewidth=0.8)

# 设置坐标轴
# ax.set_xlabel('Models', fontsize=14, fontweight='bold')
ax.set_ylabel('Average Performance Score', fontsize=20, fontweight='bold')
# ax.set_title('Performance Comparison of AI Models\n(Baseline vs Open-source vs Proprietary)', 
            #  fontsize=16, fontweight='bold', pad=20)

# 设置x轴标签
ax.set_xticks(range(len(model_names)))
ax.set_xticklabels([name.split('[')[0] for name in model_names], 
                   rotation=45, ha='right', fontsize=15,fontweight='bold')

# 设置y轴范围
ax.set_ylim(0, max(scores) * 1.1)

# 添加网格
ax.grid(True, alpha=0.3, linestyle='--', axis='y')

# 在每个柱子上添加数值标签
for i, (bar, score) in enumerate(zip(bars, scores)):
    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
            f'{score:.1f}', ha='center', va='bottom', fontsize=15, fontweight='bold')

# # 创建图例
# from matplotlib.patches import Patch
# legend_elements = [
#     Patch(facecolor='#808080', alpha=0.8, label='Baseline (Random)'),
#     Patch(facecolor='#2E86AB', alpha=0.8, label='Open-source Models'),
#     Patch(facecolor='#A23B72', alpha=0.8, label='Proprietary Models')
# ]
# ax.legend(handles=legend_elements, loc='upper left', frameon=True, 
#           fancybox=True, shadow=True, fontsize=12)

# 添加分隔线来区分不同类型的模型
baseline_end = len(baseline)
opensource_end = baseline_end + len(opensource_models)

ax.axvline(x=baseline_end - 0.5, color='red', linestyle='--', alpha=0.7, linewidth=2)
ax.axvline(x=opensource_end - 0.5, color='red', linestyle='--', alpha=0.7, linewidth=2)

# 添加区域标签
ax.text(baseline_end/2 - 0.5, 50 , 'Baseline', 
        ha='center', va='center', fontsize=20, fontweight='bold',
        bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgray', alpha=0.7))

ax.text(baseline_end + len(opensource_models)/2 - 0.5, 50 , 'Open-source', 
        ha='center', va='center', fontsize=20, fontweight='bold',
        bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7))

ax.text(opensource_end + len(proprietary_models)/2 - 0.5, 50 , 'Proprietary', 
        ha='center', va='center', fontsize=20, fontweight='bold',
        bbox=dict(boxstyle='round,pad=0.3', facecolor='lightpink', alpha=0.7))

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
plt.savefig('model_performance_comparison.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 打印统计信息
print("=== 模型性能统计 ===")
print(f"基线模型: {baseline}")
print(f"开源模型平均性能: {np.mean(list(opensource_models.values())):.1f}")
print(f"商用模型平均性能: {np.mean(list(proprietary_models.values())):.1f}")
print(f"性能提升: 开源模型比基线提升 {np.mean(list(opensource_models.values())) - baseline['Random']:.1f} 分")
print(f"性能提升: 商用模型比基线提升 {np.mean(list(proprietary_models.values())) - baseline['Random']:.1f} 分")