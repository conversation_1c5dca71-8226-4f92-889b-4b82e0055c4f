#include "process_scheduler.h"

std::vector<PCB> processes(N);
std::queue<int> readyQueue;
std::queue<int> blockedQueue;

void initializeProcesses(const std::string& filename) {
    std::ifstream file(filename);
    for (int i = 0; i < N; ++i) {
        file >> processes[i].ID >> processes[i].PRIORITY >> processes[i].CPUTIME
             >> processes[i].ALLTIME >> processes[i].STARTBLOCK >> processes[i].BLOCKTIME;
        processes[i].STATE = READY;
        readyQueue.push(i);
    }
    file.close();
}

void updatePriorities() {
    std::queue<int> tempQueue;
    while (!readyQueue.empty()) {
        int i = readyQueue.front();
        readyQueue.pop();
        processes[i].PRIORITY += 1;
        tempQueue.push(i);
    }
    readyQueue = tempQueue;
}

int selectNextProcess() {
    int maxPriority = -1;
    int selectedProcess = -1;
    std::queue<int> tempQueue;

    while (!readyQueue.empty()) {
        int i = readyQueue.front();
        readyQueue.pop();

        if (processes[i].PRIORITY > maxPriority) {
            if (selectedProcess != -1) {
                tempQueue.push(selectedProcess);
            }
            maxPriority = processes[i].PRIORITY;
            selectedProcess = i;
        } else {
            tempQueue.push(i);
        }
    }

    while (!tempQueue.empty()) {
        int i = tempQueue.front();
        tempQueue.pop();
        if (i != selectedProcess) {
            readyQueue.push(i);
        }
    }

    if (selectedProcess != -1) {
        processes[selectedProcess].PRIORITY -= 3;
    }
    return selectedProcess;
}

void runProcess(int i) {
    processes[i].CPUTIME += 1;
    processes[i].ALLTIME -= 1;

    if (processes[i].ALLTIME == 0) {
        processes[i].STATE = BLOCKED;
        return;
    }

    if (processes[i].STARTBLOCK > 0) {
        processes[i].STARTBLOCK -= 1;
        if (processes[i].STARTBLOCK == 0) {
            processes[i].STATE = BLOCKED;
            blockedQueue.push(i);
        } else {
            readyQueue.push(i);
        }
    } else if (processes[i].STARTBLOCK == -1) {
        readyQueue.push(i);
    }
}

void updateBlockedProcesses() {
    int size = blockedQueue.size();
    for (int i = 0; i < size; ++i) {
        int processIndex = blockedQueue.front();
        blockedQueue.pop();

        if (processes[processIndex].ALLTIME == 0) {
            continue;
        }

        processes[processIndex].BLOCKTIME -= 1;
        if (processes[processIndex].BLOCKTIME == 0) {
            processes[processIndex].STATE = READY;
            readyQueue.push(processIndex);
        } else {
            blockedQueue.push(processIndex);
        }
    }
}

std::vector<int> scheduleProcesses() {
    std::vector<int> schedule;

    while (!readyQueue.empty() || !blockedQueue.empty()) {
        updatePriorities();
        int selectedProcess = selectNextProcess();

        if (selectedProcess != -1) {
            schedule.push_back(processes[selectedProcess].ID);
            runProcess(selectedProcess);
        }

        updateBlockedProcesses();
    }

    return schedule;
}

int main() {
    initializeProcesses("input.txt");
    std::vector<int> schedule = scheduleProcesses();

    std::ofstream outFile("output.txt");
    for (int id : schedule) {
        outFile << id << " ";
    }
    outFile.close();

    return 0;
}
