---
type: "always_apply"
---

# Scratchpad

## 当前任务：基于图片数据生成相关性热力图

### 任务描述：
1. 从图片中提取模型性能数据
2. 计算各指标间的相关性系数
3. 绘制热力图，要求：
   - 使用与参考图一致的颜色方案
   - 纵坐标：完整指标名称(简化标识)
   - 横坐标：仅简化标识
   - 不显示数值，仅用颜色表示相关性

### 任务进度：
[ ] 数据提取和整理
[ ] 相关性计算
[ ] 热力图绘制
[ ] 代码优化和输出

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- After writing the test code and ensuring all functions are correct, you should delete the test code files to avoid cluttering the project. Meanwhile, all test files should start with "Test_".
- No matter whether the user inputs in Chinese or English, you should default to replying in Chinese, unless the user requests you to reply in English.
- Please note that it is currently July 2025.
- Be sure to use an empty card for inference; before running memory-intensive programs, check the memory usage with nvidia-smi to avoid conflicts with other programs.

## augment learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
