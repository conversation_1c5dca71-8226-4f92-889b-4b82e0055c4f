import matplotlib.pyplot as plt
import numpy as np

# --- 1. Style Configuration ---
# Use a style that mimics scientific publications
plt.style.use('seaborn-v0_8-paper')
# Set a font that is commonly used in scientific papers
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans'] # A font that has good character coverage
plt.rcParams['axes.unicode_minus'] = False


# --- 2. Data Preparation ---
# Data extracted from the user's provided image
baseline_data = {
    'Random': 19.7
}

proprietary_data = {
    'Qwen-VL-Max-latest[32f]': 45.5,
    'GPT-4o[32f]': 43.6,
    'Gemini-1.5-Pro[1 fps]': 42.5,
    'Gemini-1.5-Flash[1 fps]': 40.5,
    'Gemini-2.0-Flash[1 fps]': 38.3,
    'GPT-4o-mini[32f]': 36.5,
}

opensource_data = {
    'Kang<PERSON>[64f]': 39.2,
    'Phi-3.5-vision-instruct[32f]': 38.7,
    'LLaVA-NeXT-Video-7B-hf[32f]': 38.6,
    'Qwen2-VL-7B-Instruct[0.25 fps]': 36.2,
    'Qwen2-VL-2B-Instruct[0.5 fps]': 31.9,
    'InternVL2-Llama3-76B[32f]': 28.9,
    'InternVL2-40B[32f]': 28.4,
    'InternVL2-26B[32f]': 28.3,
    'InternVL2-8B[32f]': 28.1,
    'InternVL2-4B[32f]': 28.1,
    'InternVL2-2B[32f]': 27.6,
}

# --- 3. Data Sorting and Ordering ---
# Sort open-source models by value in descending order for the staircase effect
sorted_opensource = sorted(opensource_data.items(), key=lambda item: item[1], reverse=True)
opensource_labels, opensource_values = zip(*sorted_opensource)

# Sort proprietary models by value in descending order
sorted_proprietary = sorted(proprietary_data.items(), key=lambda item: item[1], reverse=True)
proprietary_labels, proprietary_values = zip(*sorted_proprietary)

# Combine all data categories in the specified order
# Order: Baseline -> Open-source -> Proprietary
labels = list(baseline_data.keys()) + list(opensource_labels) + list(proprietary_labels)
values = list(baseline_data.values()) + list(opensource_values) + list(proprietary_values)

# Define distinct colors for each category for clarity
colors = ['#A9A9A9'] * len(baseline_data) + \
         ['#007ACC'] * len(opensource_data) + \
         ['#D62728'] * len(proprietary_data)

# --- 4. Plot Generation ---
# Create the figure and axes objects
fig, ax = plt.subplots(figsize=(16, 9)) # Use a 16:9 aspect ratio

# Create the bar chart
bars = ax.bar(labels, values, color=colors)

# --- 5. Plot Customization for Scientific Style ---
# Set titles and labels with bold font for emphasis
ax.set_ylabel('Average Score', fontsize=14, fontweight='bold')
ax.set_title('Performance Comparison of Vision-Language Models', fontsize=18, fontweight='bold', pad=20)

# Rotate x-axis labels to prevent overlap and improve readability
plt.xticks(rotation=45, ha='right', fontsize=12)
plt.yticks(fontsize=12)

# Add a light grid on the y-axis for better value estimation
ax.yaxis.grid(True, linestyle='--', which='major', color='grey', alpha=0.6)
ax.set_axisbelow(True) # Ensure grid is drawn behind the bars

# Add data labels on top of each bar for precise values
for bar in bars:
    yval = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2.0, yval + 0.3, f'{yval:.1f}', ha='center', va='bottom', fontsize=10)

# Create custom legend handles to explain the color coding
legend_handles = [
    plt.Rectangle((0,0),1,1, color='#A9A9A9', label='Baseline'),
    plt.Rectangle((0,0),1,1, color='#007ACC', label='Open-source Models'),
    plt.Rectangle((0,0),1,1, color='#D62728', label='Proprietary Models (API)')
]
ax.legend(handles=legend_handles, fontsize=12, loc='upper left', frameon=True)

# Adjust layout to make sure all elements (like labels) fit within the figure
plt.tight_layout()

# --- 6. Display the Plot ---
# This command would display the plot in a Python environment
plt.show()